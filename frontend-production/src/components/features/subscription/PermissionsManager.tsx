import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Shield, Key, Download, Building, Eye, Copy, RefreshCw, Lock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface Permission {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  available: boolean;
  requiresPlan: string[];
  category: 'usage' | 'api' | 'export';
}

interface APIKey {
  id: string;
  name: string;
  key: string;
  created: string;
  lastUsed: string;
  callsThisMonth: number;
  limit: number;
}

interface PermissionsManagerProps {
  permissions: {
    commercialUse: boolean;
    apiAccess: boolean;
    batchExport: boolean;
  };
  onPermissionChange: (permission: string, enabled: boolean) => void;
  userPlan: string;
  availablePermissions: {
    commercialUse: boolean;
    apiAccess: boolean;
    exportFormats: string[];
    prioritySupport: boolean;
  };
  validatePermission: (permission: string) => Promise<boolean>;
  disabled?: boolean;
  className?: string;
}

const PERMISSIONS: Permission[] = [
  {
    id: 'commercialUse',
    name: '商业使用授权',
    description: '允许将生成的故事用于商业目的，包括销售、营销等',
    icon: <Building className="w-5 h-5" />,
    available: false,
    requiresPlan: ['unlimited_monthly'],
    category: 'usage'
  },
  {
    id: 'apiAccess',
    name: 'API访问权限',
    description: '通过API接口集成StoryWeaver到您的应用中',
    icon: <Key className="w-5 h-5" />,
    available: false,
    requiresPlan: ['unlimited_monthly'],
    category: 'api'
  },
  {
    id: 'batchExport',
    name: '批量导出功能',
    description: '一次性导出多个故事为不同格式文件',
    icon: <Download className="w-5 h-5" />,
    available: false,
    requiresPlan: ['pro_monthly', 'unlimited_monthly', 'pro_yearly'],
    category: 'export'
  }
];

const USAGE_TYPES = [
  {
    id: 'personal',
    name: '个人使用',
    description: '仅供个人和家庭使用',
    icon: <Eye className="w-4 h-4" />,
    available: true,
    requiresPlan: ['free', 'basic_monthly', 'pro_monthly', 'unlimited_monthly', 'pro_yearly']
  },
  {
    id: 'commercial',
    name: '商业使用',
    description: '可用于商业目的，包括销售和营销',
    icon: <Building className="w-4 h-4" />,
    available: false,
    requiresPlan: ['unlimited_monthly']
  },
  {
    id: 'educational',
    name: '教育机构使用',
    description: '学校、培训机构等教育用途',
    icon: <Shield className="w-4 h-4" />,
    available: false,
    requiresPlan: ['pro_monthly', 'unlimited_monthly', 'pro_yearly']
  }
];

export const PermissionsManager: React.FC<PermissionsManagerProps> = ({
  userPlan,
  permissions,
  apiKeys,
  onGenerateAPIKey,
  onRevokeAPIKey,
  onUpdatePermission,
  className = ''
}) => {
  const { t } = useTranslation();
  const [selectedUsageType, setSelectedUsageType] = useState('personal');
  const [newAPIKeyName, setNewAPIKeyName] = useState('');
  const [showAPIKeyForm, setShowAPIKeyForm] = useState(false);

  const isPermissionAvailable = (permission: Permission) => {
    return permission.requiresPlan.includes(userPlan);
  };

  const isUsageTypeAvailable = (usageType: any) => {
    return usageType.requiresPlan.includes(userPlan);
  };

  const handleGenerateAPIKey = () => {
    if (newAPIKeyName.trim()) {
      onGenerateAPIKey(newAPIKeyName.trim());
      setNewAPIKeyName('');
      setShowAPIKeyForm(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // 显示复制成功提示
  };

  const maskAPIKey = (key: string) => {
    return key.substring(0, 8) + '...' + key.substring(key.length - 4);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 使用授权类型 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('permissions.usageTitle', '使用授权类型')}
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {t('permissions.usageDescription', '选择您的故事使用类型，不同类型有不同的授权范围')}
        </p>

        <div className="grid gap-3">
          {USAGE_TYPES.map((usageType) => {
            const isAvailable = isUsageTypeAvailable(usageType);
            const isSelected = selectedUsageType === usageType.id;

            return (
              <Card
                key={usageType.id}
                className={`p-4 cursor-pointer transition-all duration-200 ${
                  isSelected
                    ? 'ring-2 ring-blue-500 border-blue-200 bg-blue-50'
                    : isAvailable
                    ? 'hover:border-gray-300 hover:shadow-md'
                    : 'opacity-60 cursor-not-allowed bg-gray-50'
                }`}
                onClick={() => isAvailable && setSelectedUsageType(usageType.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      isSelected ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {usageType.icon}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-gray-900">{usageType.name}</h4>
                        {!isAvailable && (
                          <Lock className="w-4 h-4 text-gray-400" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{usageType.description}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {isSelected && (
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full" />
                      </div>
                    )}
                    
                    {!isAvailable && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          // 触发升级流程
                        }}
                      >
                        升级解锁
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* 权限设置 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('permissions.settingsTitle', '权限设置')}
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {t('permissions.settingsDescription', '管理您的账户权限和功能访问')}
        </p>

        <div className="space-y-3">
          {PERMISSIONS.map((permission) => {
            const isAvailable = isPermissionAvailable(permission);
            const isEnabled = permissions[permission.id as keyof typeof permissions];

            return (
              <motion.div
                key={permission.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className={`p-4 ${!isAvailable ? 'opacity-60 bg-gray-50' : ''}`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${
                        isEnabled && isAvailable 
                          ? 'bg-green-100 text-green-600' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {permission.icon}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-gray-900">{permission.name}</h4>
                          {!isAvailable && (
                            <Lock className="w-4 h-4 text-gray-400" />
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{permission.description}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {isAvailable ? (
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={isEnabled}
                            onChange={(e) => onUpdatePermission(permission.id, e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          onClick={() => {
                            // 触发升级流程
                          }}
                        >
                          升级解锁
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* API密钥管理 */}
      {permissions.apiAccess && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {t('permissions.apiTitle', 'API密钥管理')}
              </h3>
              <p className="text-sm text-gray-600">
                {t('permissions.apiDescription', '管理您的API访问密钥')}
              </p>
            </div>
            <Button
              onClick={() => setShowAPIKeyForm(true)}
              disabled={!permissions.apiAccess}
            >
              生成新密钥
            </Button>
          </div>

          {showAPIKeyForm && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mb-4"
            >
              <Card className="p-4">
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      密钥名称
                    </label>
                    <input
                      type="text"
                      value={newAPIKeyName}
                      onChange={(e) => setNewAPIKeyName(e.target.value)}
                      placeholder="例如：我的应用API密钥"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button onClick={handleGenerateAPIKey} disabled={!newAPIKeyName.trim()}>
                      生成密钥
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowAPIKeyForm(false);
                        setNewAPIKeyName('');
                      }}
                    >
                      取消
                    </Button>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}

          <div className="space-y-3">
            {apiKeys.length === 0 ? (
              <Card className="p-8 text-center">
                <Key className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <h4 className="text-sm font-medium text-gray-900 mb-1">
                  暂无API密钥
                </h4>
                <p className="text-xs text-gray-600">
                  生成您的第一个API密钥以开始使用API服务
                </p>
              </Card>
            ) : (
              apiKeys.map((apiKey) => (
                <Card key={apiKey.id} className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900">{apiKey.name}</h4>
                        <span className="px-2 py-0.5 text-xs bg-green-100 text-green-700 rounded-full">
                          活跃
                        </span>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                            {maskAPIKey(apiKey.key)}
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(apiKey.key)}
                            className="p-1"
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                        </div>
                        <div className="text-xs text-gray-600">
                          创建于: {apiKey.created} | 最后使用: {apiKey.lastUsed}
                        </div>
                        <div className="text-xs text-gray-600">
                          本月调用: {apiKey.callsThisMonth} / {apiKey.limit}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          // 刷新密钥
                        }}
                      >
                        <RefreshCw className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => onRevokeAPIKey(apiKey.id)}
                      >
                        撤销
                      </Button>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
        </div>
      )}

      {/* 批量导出设置 */}
      {permissions.batchExport && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {t('permissions.exportTitle', '批量导出设置')}
          </h3>
          <p className="text-sm text-gray-600 mb-4">
            {t('permissions.exportDescription', '配置批量导出功能的默认设置')}
          </p>

          <Card className="p-4">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">
                  默认导出格式
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {['PDF', 'EPUB', 'MP3', 'DOCX'].map((format) => (
                    <label key={format} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        defaultChecked={format === 'PDF'}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{format}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">
                  压缩设置
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="none">不压缩</option>
                  <option value="low">低压缩</option>
                  <option value="medium">中等压缩</option>
                  <option value="high">高压缩</option>
                </select>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};