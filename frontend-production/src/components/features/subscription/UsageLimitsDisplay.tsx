import React from 'react';
import { motion } from 'framer-motion';
import { BookOpen, Users, Calendar, TrendingUp, AlertCircle, Crown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface UsageLimit {
  id: string;
  name: string;
  icon: React.ReactNode;
  current: number;
  limit: number;
  period: 'daily' | 'monthly' | 'yearly';
  description: string;
  color: string;
}

interface UsageLimitsDisplayProps {
  currentUsage: {
    storiesCreated: number;
    pagesGenerated: number;
    charactersUsed: number;
    apiCallsUsed: number;
    storageUsed: number;
  };
  limits: {
    maxStoriesPerMonth: number;
    maxPagesPerStory: number;
    maxCustomCharacters: number;
  };
  userPlan: string;
  subscription: {
    plan: string;
    status: string;
    currentPeriodEnd?: string;
  };
  className?: string;
}

const PLAN_LIMITS = {
  free: {
    storiesPerMonth: 5,
    storiesPerDay: 3,
    maxPagesPerStory: 5,
    maxCharacters: 3
  },
  basic_monthly: {
    storiesPerMonth: 50,
    storiesPerDay: 20,
    maxPagesPerStory: 10,
    maxCharacters: 5
  },
  pro_monthly: {
    storiesPerMonth: 200,
    storiesPerDay: 50,
    maxPagesPerStory: 20,
    maxCharacters: 10
  },
  unlimited_monthly: {
    storiesPerMonth: 999999,
    storiesPerDay: 999999,
    maxPagesPerStory: 999999,
    maxCharacters: 999999
  },
  pro_yearly: {
    storiesPerMonth: 200,
    storiesPerDay: 50,
    maxPagesPerStory: 20,
    maxCharacters: 10
  }
};

export const UsageLimitsDisplay: React.FC<UsageLimitsDisplayProps> = ({
  userPlan,
  usageData,
  className = ''
}) => {
  const { t } = useTranslation();
  
  const limits = PLAN_LIMITS[userPlan as keyof typeof PLAN_LIMITS] || PLAN_LIMITS.free;
  const isUnlimited = userPlan === 'unlimited_monthly';

  const usageLimits: UsageLimit[] = [
    {
      id: 'monthly_stories',
      name: '月度故事数',
      icon: <Calendar className="w-5 h-5" />,
      current: usageData.storiesThisMonth,
      limit: limits.storiesPerMonth,
      period: 'monthly',
      description: '本月已创建的故事数量',
      color: 'blue'
    },
    {
      id: 'daily_stories',
      name: '每日故事数',
      icon: <BookOpen className="w-5 h-5" />,
      current: usageData.storiesThisDay,
      limit: limits.storiesPerDay,
      period: 'daily',
      description: '今日已创建的故事数量',
      color: 'green'
    },
    {
      id: 'story_pages',
      name: '故事页数限制',
      icon: <TrendingUp className="w-5 h-5" />,
      current: 0, // 这个是限制值，不是使用量
      limit: limits.maxPagesPerStory,
      period: 'daily',
      description: '每个故事最多可包含的页数',
      color: 'purple'
    },
    {
      id: 'characters',
      name: '角色数量',
      icon: <Users className="w-5 h-5" />,
      current: usageData.charactersUsed,
      limit: limits.maxCharacters,
      period: 'monthly',
      description: '可创建的自定义角色数量',
      color: 'orange'
    }
  ];

  const getUsagePercentage = (current: number, limit: number) => {
    if (limit >= 999999) return 0; // 无限制
    return Math.min((current / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600 bg-red-100';
    if (percentage >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const getProgressBarColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const formatLimit = (limit: number) => {
    return limit >= 999999 ? '无限制' : limit.toString();
  };

  const getPlanDisplayName = (plan: string) => {
    const planNames = {
      free: '免费版',
      basic_monthly: '基础版',
      pro_monthly: '专业版',
      unlimited_monthly: '无限版',
      pro_yearly: '专业版(年付)'
    };
    return planNames[plan as keyof typeof planNames] || '未知版本';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 当前套餐信息 */}
      <Card className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Crown className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">
                当前套餐: {getPlanDisplayName(userPlan)}
              </h3>
              <p className="text-sm text-gray-600">
                {isUnlimited ? '享受无限制创作体验' : '查看您的使用情况和限制'}
              </p>
            </div>
          </div>
          {!isUnlimited && (
            <Button variant="outline" size="sm">
              升级套餐
            </Button>
          )}
        </div>
      </Card>

      {/* 使用情况概览 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {t('usage.title', '使用情况')}
        </h3>
        
        <div className="grid gap-4">
          {usageLimits.map((usage) => {
            const percentage = getUsagePercentage(usage.current, usage.limit);
            const isNearLimit = percentage >= 80 && !isUnlimited;
            const isAtLimit = percentage >= 100 && !isUnlimited;

            return (
              <motion.div
                key={usage.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className={`p-4 ${isAtLimit ? 'border-red-200 bg-red-50' : ''}`}>
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${
                        usage.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                        usage.color === 'green' ? 'bg-green-100 text-green-600' :
                        usage.color === 'purple' ? 'bg-purple-100 text-purple-600' :
                        'bg-orange-100 text-orange-600'
                      }`}>
                        {usage.icon}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{usage.name}</h4>
                        <p className="text-sm text-gray-600">{usage.description}</p>
                      </div>
                    </div>

                    {isNearLimit && (
                      <div className="flex items-center space-x-1 text-yellow-600">
                        <AlertCircle className="w-4 h-4" />
                        <span className="text-xs">接近限制</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">
                        {usage.id === 'story_pages' ? '最大页数' : '已使用'}
                      </span>
                      <span className="font-medium">
                        {usage.id === 'story_pages' 
                          ? formatLimit(usage.limit)
                          : `${usage.current} / ${formatLimit(usage.limit)}`
                        }
                      </span>
                    </div>

                    {!isUnlimited && usage.id !== 'story_pages' && (
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <motion.div
                          className={`h-2 rounded-full ${getProgressBarColor(percentage)}`}
                          initial={{ width: 0 }}
                          animate={{ width: `${percentage}%` }}
                          transition={{ duration: 0.5, delay: 0.2 }}
                        />
                      </div>
                    )}

                    {usage.id === 'story_pages' && (
                      <div className="text-xs text-gray-500">
                        每个故事最多可包含 {formatLimit(usage.limit)} 页
                      </div>
                    )}
                  </div>

                  {isAtLimit && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="mt-3 p-3 bg-red-100 rounded-lg border border-red-200"
                    >
                      <div className="flex items-center space-x-2">
                        <AlertCircle className="w-4 h-4 text-red-600" />
                        <span className="text-sm font-medium text-red-900">
                          已达到使用限制
                        </span>
                      </div>
                      <p className="text-xs text-red-700 mt-1">
                        升级到更高版本以获得更多使用额度
                      </p>
                    </motion.div>
                  )}
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* 升级提示 */}
      {!isUnlimited && (
        <Card className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <div className="text-center">
            <Crown className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <h4 className="font-semibold text-gray-900 mb-1">
              需要更多创作空间？
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              升级到更高版本，享受更多故事创作额度和高级功能
            </p>
            <div className="flex justify-center space-x-2">
              <Button variant="outline" size="sm">
                查看套餐
              </Button>
              <Button size="sm">
                立即升级
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* 使用统计 */}
      <Card className="p-4">
        <h4 className="font-medium text-gray-900 mb-3">使用统计</h4>
        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="space-y-1">
            <div className="text-2xl font-bold text-blue-600">
              {usageData.storiesThisMonth}
            </div>
            <div className="text-xs text-gray-600">本月故事</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-green-600">
              {usageData.pagesGenerated}
            </div>
            <div className="text-xs text-gray-600">总页数</div>
          </div>
        </div>
      </Card>
    </div>
  );
};