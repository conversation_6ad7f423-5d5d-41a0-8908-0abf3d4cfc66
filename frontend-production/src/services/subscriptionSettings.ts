import { api } from './api';
import { UserPreferences, UsageStats, ApiKey } from '@/types/api';

export interface SubscriptionSettingsService {
  getFeatures(): Promise<any>;
  validatePermission(permission: string): Promise<any>;
  getPreferences(): Promise<UserPreferences>;
  updatePreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences>;
  getUsageStats(): Promise<any>;
  generateApiKey(): Promise<{ apiKey: string; keyId: string; createdAt: string }>;
  getApiKeys(): Promise<any[]>;
  deleteApiKey(keyId: string): Promise<void>;
}

class SubscriptionSettingsServiceImpl implements SubscriptionSettingsService {
  /**
   * 获取订阅功能配置
   */
  async getFeatures(): Promise<any> {
    try {
      const response = await api.get('/subscription-settings/features');
      
      if (!response.data.success) {
        throw new Error(response.data.error || '获取功能配置失败');
      }
      
      return response.data.data;
    } catch (error: any) {
      console.error('获取功能配置失败:', error);
      throw new Error(error.response?.data?.error || '获取功能配置失败');
    }
  }

  /**
   * 验证用户权限
   */
  async validatePermission(permission: string): Promise<any> {
    try {
      const response = await api.post('/subscription-settings/validate-permission', { permission });
      
      if (!response.data.success) {
        throw new Error(response.data.error || '验证权限失败');
      }
      
      return response.data.data;
    } catch (error: any) {
      console.error('验证权限失败:', error);
      throw new Error(error.response?.data?.error || '验证权限失败');
    }
  }

  /**
   * 获取用户偏好设置
   */
  async getPreferences(): Promise<UserPreferences> {
    try {
      const response = await api.get('/subscription-settings/preferences');
      
      if (!response.data.success) {
        throw new Error(response.data.error || '获取偏好设置失败');
      }
      
      return response.data.data;
    } catch (error: any) {
      console.error('获取偏好设置失败:', error);
      throw new Error(error.response?.data?.error || '获取偏好设置失败');
    }
  }

  /**
   * 更新用户偏好设置
   */
  async updatePreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    try {
      const response = await api.put('/subscription-settings/preferences', preferences);
      
      if (!response.data.success) {
        throw new Error(response.data.error || '更新偏好设置失败');
      }
      
      return response.data.data;
    } catch (error: any) {
      console.error('更新偏好设置失败:', error);
      
      // 处理权限不足错误
      if (error.response?.status === 403) {
        throw new Error(error.response.data.error || '您的订阅计划不支持此功能');
      }
      
      throw new Error(error.response?.data?.error || '更新偏好设置失败');
    }
  }

  /**
   * 获取使用统计
   */
  async getUsageStats(): Promise<any> {
    try {
      const response = await api.get('/subscription-settings/usage-stats');
      
      if (!response.data.success) {
        throw new Error(response.data.error || '获取使用统计失败');
      }
      
      return response.data.data;
    } catch (error: any) {
      console.error('获取使用统计失败:', error);
      throw new Error(error.response?.data?.error || '获取使用统计失败');
    }
  }

  /**
   * 生成API密钥
   */
  async generateApiKey(): Promise<{ apiKey: string; keyId: string; createdAt: string }> {
    try {
      const response = await api.post('/subscription-settings/api-key');
      
      if (!response.data.success) {
        throw new Error(response.data.error || '生成API密钥失败');
      }
      
      return response.data.data;
    } catch (error: any) {
      console.error('生成API密钥失败:', error);
      
      // 处理权限不足错误
      if (error.response?.status === 403) {
        throw new Error('您的订阅计划不支持API访问功能');
      }
      
      throw new Error(error.response?.data?.error || '生成API密钥失败');
    }
  }

  /**
   * 获取API密钥列表
   */
  async getApiKeys(): Promise<any[]> {
    try {
      const response = await api.get('/subscription-settings/api-keys');
      
      if (!response.data.success) {
        throw new Error(response.data.error || '获取API密钥列表失败');
      }
      
      return response.data.data;
    } catch (error: any) {
      console.error('获取API密钥列表失败:', error);
      
      // 处理权限不足错误
      if (error.response?.status === 403) {
        throw new Error('您的订阅计划不支持API访问功能');
      }
      
      throw new Error(error.response?.data?.error || '获取API密钥列表失败');
    }
  }

  /**
   * 删除API密钥
   */
  async deleteApiKey(keyId: string): Promise<void> {
    try {
      const response = await api.delete(`/subscription-settings/api-key/${keyId}`);
      
      if (!response.data.success) {
        throw new Error(response.data.error || '删除API密钥失败');
      }
    } catch (error: any) {
      console.error('删除API密钥失败:', error);
      
      // 处理权限不足错误
      if (error.response?.status === 403) {
        throw new Error('您的订阅计划不支持API访问功能');
      }
      
      if (error.response?.status === 404) {
        throw new Error('API密钥不存在');
      }
      
      throw new Error(error.response?.data?.error || '删除API密钥失败');
    }
  }
}

export const subscriptionSettingsService = new SubscriptionSettingsServiceImpl();