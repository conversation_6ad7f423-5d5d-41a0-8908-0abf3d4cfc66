import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Settings, Crown, Zap, Image, Volume2, BarChart3, Shield, AlertCircle, CheckCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { DashboardLayout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useAuthStore } from '@/stores/authStore';
import { subscriptionSettingsService } from '@/services/subscriptionSettings';
import { AIModelSelector } from '@/components/features/subscription/AIModelSelector';
import { ImageQualitySelector } from '@/components/features/subscription/ImageQualitySelector';
import { AudioOptionsSelector } from '@/components/features/subscription/AudioOptionsSelector';
import { UsageLimitsDisplay } from '@/components/features/subscription/UsageLimitsDisplay';
import { PermissionsManager } from '@/components/features/subscription/PermissionsManager';
import { ServiceLevelConfig } from '@/components/features/subscription/ServiceLevelConfig';

type SettingsTab = 'ai-model' | 'image-quality' | 'audio-options' | 'usage-limits' | 'permissions' | 'service-level';

interface SubscriptionFeatures {
  subscription: {
    plan: string;
    status: string;
    currentPeriodEnd?: string;
  };
  features: {
    aiModel: {
      model: string;
      temperature: number;
      topK: number;
      maxOutputTokens: number;
    };
    imageGeneration: {
      model: string;
      resolution: string;
      quality: string;
      maxRetries: number;
      enhancedPrompt: boolean;
    };
    audioGeneration: {
      model: string;
      quality: string;
      voiceOptions: string[];
    };
    limits: {
      maxStoriesPerMonth: number;
      maxPagesPerStory: number;
      maxCustomCharacters: number;
    };
    permissions: {
      commercialUse: boolean;
      apiAccess: boolean;
      exportFormats: string[];
      prioritySupport: boolean;
    };
  };
  currentUsage: {
    storiesCreated: number;
    pagesGenerated: number;
    charactersUsed: number;
    apiCallsUsed: number;
    storageUsed: number;
  };
  limits: {
    storiesRemaining: number;
    canCreateStory: boolean;
  };
}

interface UserPreferences {
  aiModel: string;
  imageQuality: string;
  selectedVoice: string;
  audioQuality: string;
  audioSpeed: number;
  audioPitch: number;
  permissions: {
    commercialUse: boolean;
    apiAccess: boolean;
    batchExport: boolean;
  };
  language: string;
  theme: string;
  updatedAt: string;
}

const SubscriptionSettingsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const [activeTab, setActiveTab] = useState<SettingsTab>('ai-model');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // 订阅功能配置数据
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionFeatures | null>(null);
  
  // 用户偏好设置
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);

  // 加载数据
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // 并行加载订阅功能配置和用户偏好设置
      const [featuresData, preferencesData] = await Promise.all([
        subscriptionSettingsService.getFeatures(),
        subscriptionSettingsService.getPreferences()
      ]);
      
      setSubscriptionData(featuresData);
      setPreferences(preferencesData);
      
    } catch (err: any) {
      console.error('加载订阅设置数据失败:', err);
      setError(err.message || '加载数据失败，请刷新页面重试');
    } finally {
      setLoading(false);
    }
  };

  // 更新偏好设置
  const updatePreferences = async (updates: Partial<UserPreferences>) => {
    if (!preferences) return;
    
    try {
      setSaving(true);
      setError(null);
      setSuccessMessage(null);
      
      const updatedPreferences = { ...preferences, ...updates };
      const result = await subscriptionSettingsService.updatePreferences(updatedPreferences);
      
      setPreferences(result);
      setSuccessMessage('设置已保存');
      
      // 3秒后清除成功消息
      setTimeout(() => setSuccessMessage(null), 3000);
      
    } catch (err: any) {
      console.error('更新偏好设置失败:', err);
      setError(err.message || '保存设置失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  // 验证权限
  const validatePermission = async (permission: string): Promise<boolean> => {
    try {
      const result = await subscriptionSettingsService.validatePermission(permission);
      return result.hasPermission;
    } catch (err) {
      console.error('验证权限失败:', err);
      return false;
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  if (error && !subscriptionData) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <AlertCircle className="w-12 h-12 text-red-500" />
          <p className="text-red-600 text-center">{error}</p>
          <Button onClick={loadData} variant="outline">
            重新加载
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  const userPlan = subscriptionData?.subscription.plan || 'free';

  const tabs = [
    {
      id: 'ai-model' as SettingsTab,
      label: 'AI模型等级',
      icon: <Zap className="w-5 h-5" />,
      description: '选择适合的AI模型等级'
    },
    {
      id: 'image-quality' as SettingsTab,
      label: '图片生成质量',
      icon: <Image className="w-5 h-5" />,
      description: '配置图片生成质量和分辨率'
    },
    {
      id: 'audio-options' as SettingsTab,
      label: '音频朗读选项',
      icon: <Volume2 className="w-5 h-5" />,
      description: '自定义音频朗读设置'
    },
    {
      id: 'usage-limits' as SettingsTab,
      label: '使用限制管理',
      icon: <BarChart3 className="w-5 h-5" />,
      description: '查看使用量和限制'
    },
    {
      id: 'permissions' as SettingsTab,
      label: '独家权限设置',
      icon: <Shield className="w-5 h-5" />,
      description: '管理商业使用和API权限'
    },
    {
      id: 'service-level' as SettingsTab,
      label: '服务等级配置',
      icon: <Crown className="w-5 h-5" />,
      description: '客服支持和导出选项'
    }
  ];

  const renderTabContent = () => {
    if (!subscriptionData || !preferences) {
      return (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner />
        </div>
      );
    }

    switch (activeTab) {
      case 'ai-model':
        return (
          <AIModelSelector
            selectedModel={preferences.aiModel}
            onModelChange={(model) => updatePreferences({ aiModel: model })}
            userPlan={userPlan}
            availableModels={getAvailableAIModels()}
            features={subscriptionData.features.aiModel}
            disabled={saving}
          />
        );
      
      case 'image-quality':
        return (
          <ImageQualitySelector
            selectedQuality={preferences.imageQuality}
            onQualityChange={(quality) => updatePreferences({ imageQuality: quality })}
            userPlan={userPlan}
            availableQualities={getAvailableImageQualities()}
            features={subscriptionData.features.imageGeneration}
            disabled={saving}
          />
        );
      
      case 'audio-options':
        return (
          <AudioOptionsSelector
            selectedVoice={preferences.selectedVoice}
            selectedQuality={preferences.audioQuality}
            audioSpeed={preferences.audioSpeed}
            audioPitch={preferences.audioPitch}
            onVoiceChange={(voice) => updatePreferences({ selectedVoice: voice })}
            onQualityChange={(quality) => updatePreferences({ audioQuality: quality })}
            onSpeedChange={(speed) => updatePreferences({ audioSpeed: speed })}
            onPitchChange={(pitch) => updatePreferences({ audioPitch: pitch })}
            userPlan={userPlan}
            availableVoices={subscriptionData.features.audioGeneration.voiceOptions}
            features={subscriptionData.features.audioGeneration}
            disabled={saving}
          />
        );
      
      case 'usage-limits':
        return (
          <UsageLimitsDisplay
            currentUsage={subscriptionData.currentUsage}
            limits={subscriptionData.features.limits}
            userPlan={userPlan}
            subscription={subscriptionData.subscription}
          />
        );

      case 'permissions':
        return (
          <PermissionsManager
            permissions={preferences.permissions}
            onPermissionChange={(permission, enabled) => 
              updatePreferences({ 
                permissions: { ...preferences.permissions, [permission]: enabled } 
              })
            }
            userPlan={userPlan}
            availablePermissions={subscriptionData.features.permissions}
            validatePermission={validatePermission}
            disabled={saving}
          />
        );
      
      case 'service-level':
        return (
          <ServiceLevelConfig
            userPlan={userPlan}
            features={subscriptionData.features}
            subscription={subscriptionData.subscription}
          />
        );
      
      default:
        return null;
    }
  };

  // 获取可用的AI模型
  const getAvailableAIModels = () => {
    const models = ['gemini-2.5-flash'];
    if (userPlan === 'pro_monthly' || userPlan === 'pro_yearly') {
      models.push('gemini-2.5-pro');
    }
    if (userPlan === 'unlimited_monthly') {
      models.push('gemini-2.5-pro', 'gemini-2.5-ultra');
    }
    return models;
  };

  // 获取可用的图片质量
  const getAvailableImageQualities = () => {
    const qualities = ['standard'];
    if (userPlan === 'pro_monthly' || userPlan === 'pro_yearly') {
      qualities.push('premium');
    }
    if (userPlan === 'unlimited_monthly') {
      qualities.push('premium', 'ultra');
    }
    return qualities;
  };

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Settings className="w-8 h-8 text-blue-600" />
              订阅设置
            </h1>
            <p className="text-gray-600 mt-2">管理您的付费会员功能配置</p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* 成功消息 */}
            {successMessage && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg border border-green-200"
              >
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-medium">{successMessage}</span>
              </motion.div>
            )}
            
            {/* 订阅计划显示 */}
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
              <Crown className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">
                {userPlan === 'free' ? '免费版' : 
                 userPlan === 'basic_monthly' ? '基础版' :
                 userPlan === 'pro_monthly' ? '专业版' :
                 userPlan === 'unlimited_monthly' ? '无限版' : '未知计划'}
              </span>
            </div>
          </div>
        </div>

        {/* 错误消息 */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center gap-2 px-4 py-3 bg-red-50 text-red-700 rounded-lg border border-red-200"
          >
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </motion.div>
        )}

        {/* 标签导航 */}
        <Card className="p-0 overflow-hidden">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors
                    ${activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center space-x-2">
                    {tab.icon}
                    <span>{tab.label}</span>
                  </div>
                </button>
              ))}
            </nav>
          </div>

          {/* 标签内容 */}
          <div className="p-6 min-h-[500px] relative">
            {saving && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                <LoadingSpinner />
              </div>
            )}
            {renderTabContent()}
          </div>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default SubscriptionSettingsPage;