/**
 * 订阅权限中间件
 * 用于在核心功能中检查用户权限
 */

import { subscriptionSettingsService } from '@/services/subscriptionSettings';

export interface SubscriptionFeatures {
  subscription: {
    plan: string;
    status: string;
    currentPeriodEnd?: string;
  };
  features: {
    aiModel: {
      model: string;
      temperature: number;
      topK: number;
      maxOutputTokens: number;
    };
    imageGeneration: {
      model: string;
      resolution: string;
      quality: string;
      maxRetries: number;
      enhancedPrompt: boolean;
    };
    audioGeneration: {
      model: string;
      quality: string;
      voiceOptions: string[];
    };
    limits: {
      maxStoriesPerMonth: number;
      maxPagesPerStory: number;
      maxCustomCharacters: number;
    };
    permissions: {
      commercialUse: boolean;
      apiAccess: boolean;
      exportFormats: string[];
      prioritySupport: boolean;
    };
  };
  currentUsage: {
    storiesCreated: number;
    pagesGenerated: number;
    charactersUsed: number;
    apiCallsUsed: number;
    storageUsed: number;
  };
  limits: {
    storiesRemaining: number;
    canCreateStory: boolean;
  };
}

export class SubscriptionMiddleware {
  private static cachedFeatures: SubscriptionFeatures | null = null;
  private static lastFetchTime: number = 0;
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 获取用户订阅功能配置（带缓存）
   */
  static async getFeatures(): Promise<SubscriptionFeatures> {
    const now = Date.now();
    
    // 如果缓存有效，直接返回
    if (this.cachedFeatures && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.cachedFeatures;
    }

    try {
      const features = await subscriptionSettingsService.getFeatures();
      this.cachedFeatures = features;
      this.lastFetchTime = now;
      return features;
    } catch (error) {
      console.error('获取订阅功能配置失败:', error);
      // 如果有缓存，返回缓存数据
      if (this.cachedFeatures) {
        return this.cachedFeatures;
      }
      throw error;
    }
  }

  /**
   * 清除缓存
   */
  static clearCache(): void {
    this.cachedFeatures = null;
    this.lastFetchTime = 0;
  }

  /**
   * 检查是否可以创建故事
   */
  static async canCreateStory(): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: string }> {
    try {
      const features = await this.getFeatures();
      
      // 检查订阅状态
      if (features.subscription.status !== 'active' && features.subscription.plan !== 'free') {
        return {
          allowed: false,
          reason: '订阅已过期，请续费后继续使用',
          upgradeRequired: 'renew'
        };
      }

      // 检查故事创建限制
      if (!features.limits.canCreateStory) {
        const remaining = features.limits.storiesRemaining;
        if (remaining <= 0) {
          return {
            allowed: false,
            reason: `本月故事创建数量已达上限（${features.features.limits.maxStoriesPerMonth}个）`,
            upgradeRequired: features.subscription.plan === 'free' ? 'basic_monthly' : 'unlimited_monthly'
          };
        }
      }

      return { allowed: true };
    } catch (error) {
      console.error('检查故事创建权限失败:', error);
      return {
        allowed: false,
        reason: '权限检查失败，请稍后重试'
      };
    }
  }

  /**
   * 检查AI模型使用权限
   */
  static async canUseAIModel(modelId: string): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: string }> {
    try {
      const features = await this.getFeatures();
      const availableModels = this.getAvailableAIModels(features.subscription.plan);
      
      if (!availableModels.includes(modelId)) {
        const requiredPlan = this.getRequiredPlanForAIModel(modelId);
        return {
          allowed: false,
          reason: `${modelId} 模型需要 ${this.getPlanDisplayName(requiredPlan)} 或更高版本`,
          upgradeRequired: requiredPlan
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('检查AI模型权限失败:', error);
      return {
        allowed: false,
        reason: '权限检查失败，请稍后重试'
      };
    }
  }

  /**
   * 检查图片质量使用权限
   */
  static async canUseImageQuality(quality: string): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: string }> {
    try {
      const features = await this.getFeatures();
      const availableQualities = this.getAvailableImageQualities(features.subscription.plan);
      
      if (!availableQualities.includes(quality)) {
        const requiredPlan = this.getRequiredPlanForImageQuality(quality);
        return {
          allowed: false,
          reason: `${quality} 图片质量需要 ${this.getPlanDisplayName(requiredPlan)} 或更高版本`,
          upgradeRequired: requiredPlan
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('检查图片质量权限失败:', error);
      return {
        allowed: false,
        reason: '权限检查失败，请稍后重试'
      };
    }
  }

  /**
   * 检查音频选项使用权限
   */
  static async canUseVoice(voiceId: string): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: string }> {
    try {
      const features = await this.getFeatures();
      const availableVoices = features.features.audioGeneration.voiceOptions;
      
      if (!availableVoices.includes(voiceId)) {
        return {
          allowed: false,
          reason: `此声音选项需要更高版本订阅`,
          upgradeRequired: 'pro_monthly'
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('检查音频选项权限失败:', error);
      return {
        allowed: false,
        reason: '权限检查失败，请稍后重试'
      };
    }
  }

  /**
   * 检查商业使用权限
   */
  static async canUseCommercially(): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: string }> {
    try {
      const features = await this.getFeatures();
      
      if (!features.features.permissions.commercialUse) {
        return {
          allowed: false,
          reason: '商业使用权限需要无限版订阅',
          upgradeRequired: 'unlimited_monthly'
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('检查商业使用权限失败:', error);
      return {
        allowed: false,
        reason: '权限检查失败，请稍后重试'
      };
    }
  }

  /**
   * 检查API访问权限
   */
  static async canAccessAPI(): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: string }> {
    try {
      const features = await this.getFeatures();
      
      if (!features.features.permissions.apiAccess) {
        return {
          allowed: false,
          reason: 'API访问权限需要无限版订阅',
          upgradeRequired: 'unlimited_monthly'
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('检查API访问权限失败:', error);
      return {
        allowed: false,
        reason: '权限检查失败，请稍后重试'
      };
    }
  }

  /**
   * 获取可用的AI模型
   */
  private static getAvailableAIModels(plan: string): string[] {
    const models = ['gemini-2.5-flash'];
    if (plan === 'pro_monthly' || plan === 'pro_yearly') {
      models.push('gemini-2.5-pro');
    }
    if (plan === 'unlimited_monthly') {
      models.push('gemini-2.5-pro', 'gemini-2.5-ultra');
    }
    return models;
  }

  /**
   * 获取可用的图片质量
   */
  private static getAvailableImageQualities(plan: string): string[] {
    const qualities = ['standard'];
    if (plan === 'pro_monthly' || plan === 'pro_yearly') {
      qualities.push('premium');
    }
    if (plan === 'unlimited_monthly') {
      qualities.push('premium', 'ultra');
    }
    return qualities;
  }

  /**
   * 获取AI模型所需的最低计划
   */
  private static getRequiredPlanForAIModel(modelId: string): string {
    switch (modelId) {
      case 'gemini-2.5-flash':
        return 'free';
      case 'gemini-2.5-pro':
        return 'pro_monthly';
      case 'gemini-2.5-ultra':
        return 'unlimited_monthly';
      default:
        return 'free';
    }
  }

  /**
   * 获取图片质量所需的最低计划
   */
  private static getRequiredPlanForImageQuality(quality: string): string {
    switch (quality) {
      case 'standard':
        return 'free';
      case 'premium':
        return 'pro_monthly';
      case 'ultra':
        return 'unlimited_monthly';
      default:
        return 'free';
    }
  }

  /**
   * 获取计划显示名称
   */
  private static getPlanDisplayName(plan: string): string {
    switch (plan) {
      case 'free':
        return '免费版';
      case 'basic_monthly':
        return '基础版';
      case 'pro_monthly':
        return '专业版';
      case 'unlimited_monthly':
        return '无限版';
      case 'pro_yearly':
        return '专业版（年付）';
      default:
        return '未知版本';
    }
  }
}

/**
 * React Hook for subscription middleware
 */
export const useSubscriptionMiddleware = () => {
  return {
    getFeatures: SubscriptionMiddleware.getFeatures,
    canCreateStory: SubscriptionMiddleware.canCreateStory,
    canUseAIModel: SubscriptionMiddleware.canUseAIModel,
    canUseImageQuality: SubscriptionMiddleware.canUseImageQuality,
    canUseVoice: SubscriptionMiddleware.canUseVoice,
    canUseCommercially: SubscriptionMiddleware.canUseCommercially,
    canAccessAPI: SubscriptionMiddleware.canAccessAPI,
    clearCache: SubscriptionMiddleware.clearCache
  };
};